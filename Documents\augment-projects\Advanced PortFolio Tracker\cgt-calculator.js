// Australian CGT Calculator - Main JavaScript File

class CGTCalculator {
    constructor() {
        this.calculations = [];
        this.portfolioData = null;
        
        this.initializeEventListeners();
        this.updateTaxYear();
        this.loadPortfolioData();
    }

    initializeEventListeners() {
        // Navigation
        document.getElementById('backToPortfolio').addEventListener('click', () => {
            window.location.href = 'index.html';
        });

        // Form handling
        document.getElementById('cgtForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculateSingleCGT();
        });

        document.getElementById('clearForm').addEventListener('click', () => {
            this.clearForm();
        });

        document.getElementById('addToCalculation').addEventListener('click', () => {
            this.addToCalculationList();
        });

        // Bulk operations
        document.getElementById('calculateAllCGT').addEventListener('click', () => {
            this.calculateAllFromPortfolio();
        });

        document.getElementById('exportCGT').addEventListener('click', () => {
            this.exportCGTReport();
        });

        // Close error modal
        document.getElementById('closeErrorModal').addEventListener('click', () => {
            this.hideModal('errorModal');
        });
    }

    async loadPortfolioData() {
        """Load portfolio data from the main application"""
        try {
            const response = await fetch('/api/load-csv');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Parse CSV data to get portfolio holdings
                    Papa.parse(data.csv_content, {
                        header: true,
                        skipEmptyLines: true,
                        complete: (results) => {
                            this.portfolioData = results.data;
                            console.log('Portfolio data loaded for CGT calculations');
                        }
                    });
                }
            }
        } catch (error) {
            console.log('No portfolio data available for CGT calculations');
        }
    }

    calculateSingleCGT() {
        """Calculate CGT for a single transaction"""
        try {
            const formData = this.getFormData();
            const result = this.performCGTCalculation(formData);
            
            this.displaySingleResult(result);
            
        } catch (error) {
            this.showError('Error calculating CGT: ' + error.message);
        }
    }

    getFormData() {
        """Extract data from the form"""
        const assetName = document.getElementById('assetName').value.trim();
        const purchaseDateStr = document.getElementById('purchaseDate').value;
        const saleDateStr = document.getElementById('saleDate').value;
        const quantityStr = document.getElementById('quantity').value;
        const purchasePriceStr = document.getElementById('purchasePrice').value;
        const salePriceStr = document.getElementById('salePrice').value;
        const purchaseCostsStr = document.getElementById('purchaseCosts').value;
        const saleCostsStr = document.getElementById('saleCosts').value;
        const improvementCostsStr = document.getElementById('improvementCosts').value;

        // Comprehensive validation
        if (!assetName || assetName.length === 0) {
            throw new Error('Asset name is required and cannot be empty');
        }

        if (assetName.length > 20) {
            throw new Error('Asset name cannot exceed 20 characters');
        }

        if (!purchaseDateStr) {
            throw new Error('Purchase date is required');
        }

        if (!saleDateStr) {
            throw new Error('Sale date is required');
        }

        const purchaseDate = new Date(purchaseDateStr);
        const saleDate = new Date(saleDateStr);

        if (isNaN(purchaseDate.getTime())) {
            throw new Error('Invalid purchase date format');
        }

        if (isNaN(saleDate.getTime())) {
            throw new Error('Invalid sale date format');
        }

        // Date validation
        const today = new Date();
        today.setHours(23, 59, 59, 999); // End of today

        if (purchaseDate > today) {
            throw new Error('Purchase date cannot be in the future');
        }

        if (saleDate > today) {
            throw new Error('Sale date cannot be in the future');
        }

        if (saleDate <= purchaseDate) {
            throw new Error('Sale date must be after purchase date');
        }

        // Validate holding period is reasonable (not more than 50 years)
        const maxHoldingPeriod = 50 * 365 * 24 * 60 * 60 * 1000; // 50 years in milliseconds
        if (saleDate.getTime() - purchaseDate.getTime() > maxHoldingPeriod) {
            throw new Error('Holding period cannot exceed 50 years');
        }

        // Numeric validation
        if (!quantityStr || quantityStr.trim() === '') {
            throw new Error('Quantity is required');
        }

        const quantity = parseFloat(quantityStr);
        if (isNaN(quantity) || quantity <= 0) {
            throw new Error('Quantity must be a positive number');
        }

        if (quantity > 10000000) {
            throw new Error('Quantity cannot exceed 10,000,000 shares');
        }

        if (!purchasePriceStr || purchasePriceStr.trim() === '') {
            throw new Error('Purchase price is required');
        }

        const purchasePrice = parseFloat(purchasePriceStr);
        if (isNaN(purchasePrice) || purchasePrice <= 0) {
            throw new Error('Purchase price must be a positive number');
        }

        if (purchasePrice > 100000) {
            throw new Error('Purchase price cannot exceed $100,000 per share');
        }

        if (!salePriceStr || salePriceStr.trim() === '') {
            throw new Error('Sale price is required');
        }

        const salePrice = parseFloat(salePriceStr);
        if (isNaN(salePrice) || salePrice <= 0) {
            throw new Error('Sale price must be a positive number');
        }

        if (salePrice > 100000) {
            throw new Error('Sale price cannot exceed $100,000 per share');
        }

        // Optional fields validation
        const purchaseCosts = parseFloat(purchaseCostsStr) || 0;
        const saleCosts = parseFloat(saleCostsStr) || 0;
        const improvementCosts = parseFloat(improvementCostsStr) || 0;

        if (purchaseCosts < 0) {
            throw new Error('Purchase costs cannot be negative');
        }

        if (saleCosts < 0) {
            throw new Error('Sale costs cannot be negative');
        }

        if (improvementCosts < 0) {
            throw new Error('Improvement costs cannot be negative');
        }

        // Sanity check on total costs
        const totalCosts = purchaseCosts + saleCosts + improvementCosts;
        const totalValue = quantity * Math.max(purchasePrice, salePrice);

        if (totalCosts > totalValue) {
            console.warn('Total costs exceed transaction value - please verify amounts');
        }

        return {
            assetName: assetName.toUpperCase(),
            purchaseDate,
            saleDate,
            quantity,
            purchasePrice,
            salePrice,
            purchaseCosts,
            saleCosts,
            improvementCosts
        };
    }

    performCGTCalculation(data) {
        """Perform the actual CGT calculation using Australian tax rules"""
        const {
            assetName,
            purchaseDate,
            saleDate,
            quantity,
            purchasePrice,
            salePrice,
            purchaseCosts,
            saleCosts,
            improvementCosts
        } = data;

        // Calculate holding period
        const holdingPeriodMs = saleDate.getTime() - purchaseDate.getTime();
        const holdingPeriodDays = Math.floor(holdingPeriodMs / (1000 * 60 * 60 * 24));
        const holdingPeriodYears = holdingPeriodDays / 365.25;

        // Calculate cost base (purchase price + costs)
        const costBase = (quantity * purchasePrice) + purchaseCosts + improvementCosts;

        // Calculate proceeds (sale price - costs)
        const proceeds = (quantity * salePrice) - saleCosts;

        // Calculate capital gain/loss
        const capitalGain = proceeds - costBase;

        // Determine if CGT discount applies (50% for assets held > 12 months)
        const isLongTerm = holdingPeriodDays > 365;
        const cgtDiscountApplies = isLongTerm && capitalGain > 0;

        // Calculate taxable capital gain
        let taxableGain = capitalGain;
        if (cgtDiscountApplies) {
            taxableGain = capitalGain * 0.5; // 50% CGT discount
        }

        // If it's a loss, no discount applies
        if (capitalGain < 0) {
            taxableGain = capitalGain;
        }

        return {
            assetName,
            purchaseDate,
            saleDate,
            quantity,
            holdingPeriodDays,
            holdingPeriodYears,
            costBase,
            proceeds,
            capitalGain,
            isLongTerm,
            cgtDiscountApplies,
            taxableGain,
            purchasePrice,
            salePrice,
            purchaseCosts,
            saleCosts,
            improvementCosts
        };
    }

    displaySingleResult(result) {
        """Display the calculation result in the form"""
        const resultDiv = document.getElementById('cgtResult');
        
        // Update result fields
        document.getElementById('holdingPeriod').textContent = 
            `${result.holdingPeriodDays} days (${result.holdingPeriodYears.toFixed(2)} years)`;
        
        document.getElementById('costBase').textContent = this.formatCurrency(result.costBase);
        document.getElementById('proceeds').textContent = this.formatCurrency(result.proceeds);
        
        const capitalGainElement = document.getElementById('capitalGain');
        capitalGainElement.textContent = this.formatCurrency(result.capitalGain);
        capitalGainElement.className = `result-value ${result.capitalGain >= 0 ? 'positive' : 'negative'}`;
        
        document.getElementById('cgtDiscount').textContent = 
            result.cgtDiscountApplies ? 'Yes (50%)' : 'No';
        
        const taxableGainElement = document.getElementById('taxableGain');
        taxableGainElement.textContent = this.formatCurrency(result.taxableGain);
        taxableGainElement.className = `result-value ${result.taxableGain >= 0 ? 'positive' : 'negative'}`;
        
        // Show result
        resultDiv.classList.remove('hidden');
    }

    addToCalculationList() {
        """Add the current calculation to the list"""
        try {
            const formData = this.getFormData();
            const result = this.performCGTCalculation(formData);
            
            this.calculations.push(result);
            this.updateCalculationsList();
            this.updateSummaryTotals();
            this.clearForm();
            
            this.showSuccessMessage(`CGT calculation added for ${result.assetName}`);
            
        } catch (error) {
            this.showError('Error adding calculation: ' + error.message);
        }
    }

    updateCalculationsList() {
        """Update the calculations list display"""
        const container = document.getElementById('calculationsList');
        
        if (this.calculations.length === 0) {
            container.innerHTML = '<p class="no-calculations">No CGT calculations yet. Use the form to add calculations.</p>';
            document.getElementById('calculationCount').textContent = '0 calculations';
            return;
        }

        container.innerHTML = '';
        
        this.calculations.forEach((calc, index) => {
            const item = this.createCalculationItem(calc, index);
            container.appendChild(item);
        });
        
        document.getElementById('calculationCount').textContent = 
            `${this.calculations.length} calculation${this.calculations.length !== 1 ? 's' : ''}`;
    }

    createCalculationItem(calc, index) {
        """Create a calculation item element"""
        const item = document.createElement('div');
        item.className = 'calculation-item';
        
        item.innerHTML = `
            <div class="calculation-header">
                <span class="calculation-asset">${calc.assetName}</span>
                <span class="calculation-date">${calc.saleDate.toLocaleDateString()}</span>
            </div>
            <div class="calculation-details">
                <div class="calculation-detail">
                    <span class="detail-label">Quantity:</span>
                    <span class="detail-value">${this.formatNumber(calc.quantity)}</span>
                </div>
                <div class="calculation-detail">
                    <span class="detail-label">Holding Period:</span>
                    <span class="detail-value">${calc.holdingPeriodDays} days</span>
                </div>
                <div class="calculation-detail">
                    <span class="detail-label">Capital Gain:</span>
                    <span class="detail-value ${calc.capitalGain >= 0 ? 'positive' : 'negative'}">
                        ${this.formatCurrency(calc.capitalGain)}
                    </span>
                </div>
                <div class="calculation-detail">
                    <span class="detail-label">Taxable Gain:</span>
                    <span class="detail-value ${calc.taxableGain >= 0 ? 'positive' : 'negative'}">
                        ${this.formatCurrency(calc.taxableGain)}
                    </span>
                </div>
            </div>
        `;
        
        return item;
    }

    updateSummaryTotals() {
        """Update the summary totals"""
        let totalGains = 0;
        let totalLosses = 0;
        let totalTaxableGain = 0;

        this.calculations.forEach(calc => {
            if (calc.capitalGain > 0) {
                totalGains += calc.capitalGain;
            } else {
                totalLosses += Math.abs(calc.capitalGain);
            }
            totalTaxableGain += calc.taxableGain;
        });

        const netCapitalGain = totalGains - totalLosses;

        document.getElementById('totalGains').textContent = this.formatCurrency(totalGains);
        document.getElementById('totalLosses').textContent = this.formatCurrency(totalLosses);
        
        const netElement = document.getElementById('netCapitalGain');
        netElement.textContent = this.formatCurrency(netCapitalGain);
        netElement.className = `total-value ${netCapitalGain >= 0 ? 'positive' : 'negative'}`;
        
        const taxableElement = document.getElementById('totalTaxableGain');
        taxableElement.textContent = this.formatCurrency(totalTaxableGain);
        taxableElement.className = `total-value ${totalTaxableGain >= 0 ? 'positive' : 'negative'}`;
    }

    clearForm() {
        """Clear the CGT form"""
        document.getElementById('cgtForm').reset();
        document.getElementById('cgtResult').classList.add('hidden');
    }

    updateTaxYear() {
        """Update the tax year display"""
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();
        
        // Australian tax year runs from July 1 to June 30
        const taxYear = month >= 6 ? `${year}-${(year + 1).toString().slice(-2)}` : `${year - 1}-${year.toString().slice(-2)}`;
        document.getElementById('taxYear').textContent = taxYear;
    }

    // Utility methods
    formatCurrency(value) {
        return new Intl.NumberFormat('en-AU', {
            style: 'currency',
            currency: 'AUD'
        }).format(value || 0);
    }

    formatNumber(value) {
        return new Intl.NumberFormat('en-AU').format(value || 0);
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorModal').classList.add('show');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    showSuccessMessage(message) {
        // Create a temporary success notification
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: var(--primary-bg);
            padding: 1rem 1.5rem;
            border-radius: 4px;
            font-weight: bold;
            z-index: 1002;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    calculateAllFromPortfolio() {
        """Calculate CGT for all sold positions from portfolio data"""
        if (!this.portfolioData || this.portfolioData.length === 0) {
            this.showError('No portfolio data available. Please load portfolio data first.');
            return;
        }

        try {
            this.showLoading();

            // Group transactions by stock code
            const stockTransactions = new Map();

            this.portfolioData.forEach(transaction => {
                const code = transaction['Code'].trim().toUpperCase();
                if (!stockTransactions.has(code)) {
                    stockTransactions.set(code, []);
                }
                stockTransactions.get(code).push({
                    date: new Date(transaction['Date']),
                    quantity: parseFloat(transaction['Quantity']) || 0,
                    action: transaction['Action'].toLowerCase().trim(),
                    price: parseFloat(transaction['Avg. price']) || 0,
                    fees: parseFloat(transaction['Fees']) || 0
                });
            });

            // Calculate CGT for each stock with sell transactions
            let calculationsAdded = 0;

            stockTransactions.forEach((transactions, code) => {
                const sellTransactions = transactions.filter(t => t.action === 'sell');
                const buyTransactions = transactions.filter(t => t.action === 'buy').sort((a, b) => a.date - b.date);

                sellTransactions.forEach(sellTx => {
                    // Find matching buy transaction (FIFO method)
                    let remainingQuantity = sellTx.quantity;
                    let totalCostBase = 0;
                    let earliestPurchaseDate = null;

                    for (const buyTx of buyTransactions) {
                        if (remainingQuantity <= 0) break;

                        const quantityToUse = Math.min(remainingQuantity, buyTx.quantity);
                        totalCostBase += (quantityToUse * buyTx.price) + (buyTx.fees * (quantityToUse / buyTx.quantity));

                        if (!earliestPurchaseDate || buyTx.date < earliestPurchaseDate) {
                            earliestPurchaseDate = buyTx.date;
                        }

                        remainingQuantity -= quantityToUse;
                        buyTx.quantity -= quantityToUse; // Reduce available quantity
                    }

                    if (earliestPurchaseDate && remainingQuantity <= 0) {
                        // Create CGT calculation
                        const cgtData = {
                            assetName: code,
                            purchaseDate: earliestPurchaseDate,
                            saleDate: sellTx.date,
                            quantity: sellTx.quantity,
                            purchasePrice: totalCostBase / sellTx.quantity,
                            salePrice: sellTx.price,
                            purchaseCosts: 0, // Already included in purchase price
                            saleCosts: sellTx.fees,
                            improvementCosts: 0
                        };

                        const result = this.performCGTCalculation(cgtData);
                        this.calculations.push(result);
                        calculationsAdded++;
                    }
                });
            });

            this.updateCalculationsList();
            this.updateSummaryTotals();
            this.hideLoading();

            if (calculationsAdded > 0) {
                this.showSuccessMessage(`Added ${calculationsAdded} CGT calculations from portfolio data`);
            } else {
                this.showError('No sell transactions found in portfolio data');
            }

        } catch (error) {
            this.hideLoading();
            this.showError('Error calculating CGT from portfolio: ' + error.message);
        }
    }

    exportCGTReport() {
        """Export CGT calculations as a detailed report"""
        if (this.calculations.length === 0) {
            this.showError('No calculations to export. Please add some CGT calculations first.');
            return;
        }

        try {
            const report = this.generateCGTReport();

            // Create and download the report
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `CGT-Report-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showSuccessMessage('CGT report exported successfully');

        } catch (error) {
            this.showError('Error exporting CGT report: ' + error.message);
        }
    }

    generateCGTReport() {
        """Generate a detailed CGT report"""
        const now = new Date();
        const taxYear = document.getElementById('taxYear').textContent;

        let report = `AUSTRALIAN CAPITAL GAINS TAX REPORT\n`;
        report += `Generated: ${now.toLocaleString()}\n`;
        report += `Tax Year: ${taxYear}\n`;
        report += `Currency: Australian Dollars (AUD)\n`;
        report += `\n${'='.repeat(80)}\n\n`;

        // Summary section
        let totalGains = 0;
        let totalLosses = 0;
        let totalTaxableGain = 0;

        this.calculations.forEach(calc => {
            if (calc.capitalGain > 0) {
                totalGains += calc.capitalGain;
            } else {
                totalLosses += Math.abs(calc.capitalGain);
            }
            totalTaxableGain += calc.taxableGain;
        });

        const netCapitalGain = totalGains - totalLosses;

        report += `SUMMARY\n`;
        report += `${'='.repeat(40)}\n`;
        report += `Total Capital Gains: ${this.formatCurrency(totalGains)}\n`;
        report += `Total Capital Losses: ${this.formatCurrency(totalLosses)}\n`;
        report += `Net Capital Gain/Loss: ${this.formatCurrency(netCapitalGain)}\n`;
        report += `Total Taxable Capital Gain: ${this.formatCurrency(totalTaxableGain)}\n`;
        report += `Number of Transactions: ${this.calculations.length}\n\n`;

        // Detailed calculations
        report += `DETAILED CALCULATIONS\n`;
        report += `${'='.repeat(40)}\n\n`;

        this.calculations.forEach((calc, index) => {
            report += `${index + 1}. ${calc.assetName}\n`;
            report += `   Purchase Date: ${calc.purchaseDate.toLocaleDateString()}\n`;
            report += `   Sale Date: ${calc.saleDate.toLocaleDateString()}\n`;
            report += `   Holding Period: ${calc.holdingPeriodDays} days (${calc.holdingPeriodYears.toFixed(2)} years)\n`;
            report += `   Quantity: ${this.formatNumber(calc.quantity)}\n`;
            report += `   Purchase Price: ${this.formatCurrency(calc.purchasePrice)} per unit\n`;
            report += `   Sale Price: ${this.formatCurrency(calc.salePrice)} per unit\n`;
            report += `   Cost Base: ${this.formatCurrency(calc.costBase)}\n`;
            report += `   Proceeds: ${this.formatCurrency(calc.proceeds)}\n`;
            report += `   Capital Gain/Loss: ${this.formatCurrency(calc.capitalGain)}\n`;
            report += `   CGT Discount Applied: ${calc.cgtDiscountApplies ? 'Yes (50%)' : 'No'}\n`;
            report += `   Taxable Capital Gain: ${this.formatCurrency(calc.taxableGain)}\n`;
            report += `\n`;
        });

        report += `${'='.repeat(80)}\n`;
        report += `IMPORTANT NOTES:\n`;
        report += `- This report is for informational purposes only\n`;
        report += `- Please consult a qualified tax professional for tax advice\n`;
        report += `- CGT discount of 50% applies to assets held for more than 12 months\n`;
        report += `- Capital losses can be used to offset capital gains\n`;
        report += `- Unused capital losses can be carried forward to future years\n`;

        return report;
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('show');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }
}

// Initialize the CGT calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CGTCalculator();
});
